
import requests
import json

base_url = 'https://restapi.getui.com/v2/xwdkpQLlfk8Azm2eErDqq9'

def get_token(sign="", timestamp="", appkey=""):
    """
    获取token的方法，发送POST请求

    Args:
        sign (str): 签名参数
        timestamp (str): 时间戳参数
        appkey (str): 应用密钥参数

    Returns:
        dict: 响应结果
    """
    token_url = f'{base_url}/auth'

    # 请求头设置
    headers = {
        'Content-Type': 'application/json;charset=utf-8'
    }

    # 请求参数
    payload = {
        "sign": sign,
        "timestamp": timestamp,
        "appkey": appkey
    }

    try:
        # 发送POST请求
        response = requests.post(
            url=token_url,
            headers=headers,
            data=json.dumps(payload)
        )

        # 返回JSON响应
        return response.json()

    except requests.exceptions.RequestException as e:
        print(f"请求失败: {e}")
        return None
    except json.JSONDecodeError as e:
        print(f"JSON解析失败: {e}")
        return None
