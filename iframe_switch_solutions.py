from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException
import time

def switch_to_iframe_method1(browser, timeout=20):
    """
    方法1: 使用name属性切换iframe
    """
    try:
        print("方法1: 尝试通过name属性切换iframe...")
        # 等待iframe出现并切换
        iframe = WebDriverWait(browser, timeout).until(
            EC.frame_to_be_available_and_switch_to_it((By.NAME, "opendoc-d2343b7f-5a10-44f9-ace6-e18c2c646556"))
        )
        print("方法1: 成功切换到iframe")
        return True
    except TimeoutException:
        print("方法1: 通过name属性切换失败")
        return False

def switch_to_iframe_method2(browser, timeout=20):
    """
    方法2: 使用更精确的XPath
    """
    try:
        print("方法2: 尝试通过精确XPath切换iframe...")
        # 更精确的XPath定位
        iframe_xpath = "//iframe[@name='opendoc-d2343b7f-5a10-44f9-ace6-e18c2c646556']"
        iframe = WebDriverWait(browser, timeout).until(
            EC.frame_to_be_available_and_switch_to_it((By.XPATH, iframe_xpath))
        )
        print("方法2: 成功切换到iframe")
        return True
    except TimeoutException:
        print("方法2: 通过精确XPath切换失败")
        return False

def switch_to_iframe_method3(browser, timeout=20):
    """
    方法3: 使用部分name匹配
    """
    try:
        print("方法3: 尝试通过部分name匹配切换iframe...")
        # 使用contains匹配name属性
        iframe_xpath = "//iframe[contains(@name, 'opendoc-')]"
        iframe = WebDriverWait(browser, timeout).until(
            EC.frame_to_be_available_and_switch_to_it((By.XPATH, iframe_xpath))
        )
        print("方法3: 成功切换到iframe")
        return True
    except TimeoutException:
        print("方法3: 通过部分name匹配切换失败")
        return False

def switch_to_iframe_method4(browser, timeout=20):
    """
    方法4: 先等待元素存在，再切换
    """
    try:
        print("方法4: 先等待iframe元素存在...")
        # 先等待iframe元素存在
        iframe_element = WebDriverWait(browser, timeout).until(
            EC.presence_of_element_located((By.XPATH, "//iframe[contains(@name, 'opendoc-')]"))
        )
        print("方法4: iframe元素已找到，尝试切换...")
        
        # 切换到iframe
        browser.switch_to.frame(iframe_element)
        print("方法4: 成功切换到iframe")
        return True
    except TimeoutException:
        print("方法4: iframe元素未找到或切换失败")
        return False

def switch_to_iframe_method5(browser, timeout=20):
    """
    方法5: 逐步等待和切换
    """
    try:
        print("方法5: 逐步等待各层元素...")
        
        # 先等待外层容器
        WebDriverWait(browser, timeout).until(
            EC.presence_of_element_located((By.ID, "lark_doc"))
        )
        print("方法5: lark_doc容器已找到")
        
        # 等待tt-docs-component
        WebDriverWait(browser, timeout).until(
            EC.presence_of_element_located((By.TAG_NAME, "tt-docs-component"))
        )
        print("方法5: tt-docs-component已找到")
        
        # 等待并切换到iframe
        iframe = WebDriverWait(browser, timeout).until(
            EC.frame_to_be_available_and_switch_to_it((By.XPATH, "//tt-docs-component//iframe"))
        )
        print("方法5: 成功切换到iframe")
        return True
    except TimeoutException:
        print("方法5: 逐步等待失败")
        return False

def switch_to_iframe_method6(browser, timeout=20):
    """
    方法6: 等待页面完全加载后再切换
    """
    try:
        print("方法6: 等待页面完全加载...")
        
        # 等待页面加载完成
        WebDriverWait(browser, timeout).until(
            lambda driver: driver.execute_script("return document.readyState") == "complete"
        )
        print("方法6: 页面加载完成")
        
        # 额外等待一段时间确保iframe完全渲染
        time.sleep(3)
        
        # 尝试切换iframe
        iframe = WebDriverWait(browser, timeout).until(
            EC.frame_to_be_available_and_switch_to_it((By.XPATH, "//iframe[contains(@name, 'opendoc-')]"))
        )
        print("方法6: 成功切换到iframe")
        return True
    except TimeoutException:
        print("方法6: 等待页面加载后切换失败")
        return False

def try_all_methods(browser):
    """
    尝试所有方法切换iframe
    """
    methods = [
        switch_to_iframe_method1,
        switch_to_iframe_method2,
        switch_to_iframe_method3,
        switch_to_iframe_method4,
        switch_to_iframe_method5,
        switch_to_iframe_method6
    ]
    
    for i, method in enumerate(methods, 1):
        print(f"\n=== 尝试方法 {i} ===")
        try:
            # 确保回到主文档
            browser.switch_to.default_content()
            
            if method(browser):
                print(f"✅ 方法 {i} 成功!")
                return True
        except Exception as e:
            print(f"❌ 方法 {i} 出现异常: {e}")
            continue
    
    print("\n❌ 所有方法都失败了")
    return False

def debug_iframe_info(browser):
    """
    调试函数：打印页面中所有iframe的信息
    """
    try:
        print("\n=== 调试信息：查找页面中的所有iframe ===")
        
        # 回到主文档
        browser.switch_to.default_content()
        
        # 查找所有iframe
        iframes = browser.find_elements(By.TAG_NAME, "iframe")
        print(f"找到 {len(iframes)} 个iframe:")
        
        for i, iframe in enumerate(iframes):
            name = iframe.get_attribute("name") or "无name属性"
            src = iframe.get_attribute("src") or "无src属性"
            id_attr = iframe.get_attribute("id") or "无id属性"
            print(f"  iframe {i+1}:")
            print(f"    name: {name}")
            print(f"    id: {id_attr}")
            print(f"    src: {src[:100]}..." if len(src) > 100 else f"    src: {src}")
            print()
            
    except Exception as e:
        print(f"调试信息获取失败: {e}")

# 使用示例
if __name__ == "__main__":
    # 假设您已经有了browser对象
    # browser = webdriver.Chrome()  # 或其他浏览器
    # browser.get("your_url_here")
    
    # 调试iframe信息
    # debug_iframe_info(browser)
    
    # 尝试所有方法
    # success = try_all_methods(browser)
    
    # if success:
    #     print("成功切换到iframe，可以继续后续操作")
    # else:
    #     print("无法切换到iframe，请检查页面结构")
    
    pass
