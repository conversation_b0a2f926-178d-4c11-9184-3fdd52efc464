from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException
import time

def switch_to_iframe_method1(browser, timeout=20):
    """
    方法1: 使用name属性切换iframe
    """
    try:
        print("方法1: 尝试通过name属性切换iframe...")
        # 等待iframe出现并切换
        iframe = WebDriverWait(browser, timeout).until(
            EC.frame_to_be_available_and_switch_to_it((By.NAME, "opendoc-d2343b7f-5a10-44f9-ace6-e18c2c646556"))
        )
        print("方法1: 成功切换到iframe")
        return True
    except TimeoutException:
        print("方法1: 通过name属性切换失败")
        return False

def switch_to_iframe_method2(browser, timeout=20):
    """
    方法2: 使用更精确的XPath
    """
    try:
        print("方法2: 尝试通过精确XPath切换iframe...")
        # 更精确的XPath定位
        iframe_xpath = "//iframe[@name='opendoc-d2343b7f-5a10-44f9-ace6-e18c2c646556']"
        iframe = WebDriverWait(browser, timeout).until(
            EC.frame_to_be_available_and_switch_to_it((By.XPATH, iframe_xpath))
        )
        print("方法2: 成功切换到iframe")
        return True
    except TimeoutException:
        print("方法2: 通过精确XPath切换失败")
        return False

def switch_to_iframe_method3(browser, timeout=20):
    """
    方法3: 使用部分name匹配
    """
    try:
        print("方法3: 尝试通过部分name匹配切换iframe...")
        # 使用contains匹配name属性
        iframe_xpath = "//iframe[contains(@name, 'opendoc-')]"
        iframe = WebDriverWait(browser, timeout).until(
            EC.frame_to_be_available_and_switch_to_it((By.XPATH, iframe_xpath))
        )
        print("方法3: 成功切换到iframe")
        return True
    except TimeoutException:
        print("方法3: 通过部分name匹配切换失败")
        return False

def switch_to_iframe_method4(browser, timeout=20):
    """
    方法4: 先等待元素存在，再切换
    """
    try:
        print("方法4: 先等待iframe元素存在...")
        # 先等待iframe元素存在
        iframe_element = WebDriverWait(browser, timeout).until(
            EC.presence_of_element_located((By.XPATH, "//iframe[contains(@name, 'opendoc-')]"))
        )
        print("方法4: iframe元素已找到，尝试切换...")
        
        # 切换到iframe
        browser.switch_to.frame(iframe_element)
        print("方法4: 成功切换到iframe")
        return True
    except TimeoutException:
        print("方法4: iframe元素未找到或切换失败")
        return False

def switch_to_iframe_method5(browser, timeout=20):
    """
    方法5: 逐步等待和切换
    """
    try:
        print("方法5: 逐步等待各层元素...")

        # 先等待外层容器
        WebDriverWait(browser, timeout).until(
            EC.presence_of_element_located((By.ID, "lark_doc"))
        )
        print("方法5: lark_doc容器已找到")

        # 等待tt-docs-component
        WebDriverWait(browser, timeout).until(
            EC.presence_of_element_located((By.TAG_NAME, "tt-docs-component"))
        )
        print("方法5: tt-docs-component已找到")

        # 调试：查看tt-docs-component内部的iframe
        print("方法5: 查找tt-docs-component内部的iframe...")
        tt_docs = browser.find_element(By.TAG_NAME, "tt-docs-component")
        iframes_in_component = tt_docs.find_elements(By.TAG_NAME, "iframe")
        print(f"方法5: tt-docs-component内找到 {len(iframes_in_component)} 个iframe")

        if len(iframes_in_component) == 0:
            print("方法5: tt-docs-component内没有直接的iframe，查找更深层的iframe...")
            # 查找所有iframe
            all_iframes = browser.find_elements(By.TAG_NAME, "iframe")
            print(f"方法5: 页面总共找到 {len(all_iframes)} 个iframe")

            for i, iframe in enumerate(all_iframes):
                name = iframe.get_attribute("name") or "无name"
                src = iframe.get_attribute("src") or "无src"
                print(f"方法5: iframe {i+1} - name: {name[:50]}...")

                # 尝试切换到每个iframe
                try:
                    browser.switch_to.frame(iframe)
                    print(f"方法5: 成功切换到iframe {i+1}")
                    return True
                except Exception as e:
                    print(f"方法5: 切换到iframe {i+1}失败: {e}")
                    browser.switch_to.default_content()
                    continue
        else:
            # 等待并切换到iframe
            iframe = WebDriverWait(browser, timeout).until(
                EC.frame_to_be_available_and_switch_to_it((By.XPATH, "//tt-docs-component//iframe"))
            )
            print("方法5: 成功切换到iframe")
            return True

        print("方法5: 所有iframe都尝试失败")
        return False
    except TimeoutException:
        print("方法5: 逐步等待失败")
        return False
    except Exception as e:
        print(f"方法5: 出现异常: {e}")
        return False

def switch_to_iframe_method6(browser, timeout=20):
    """
    方法6: 等待页面完全加载后再切换
    """
    try:
        print("方法6: 等待页面完全加载...")
        
        # 等待页面加载完成
        WebDriverWait(browser, timeout).until(
            lambda driver: driver.execute_script("return document.readyState") == "complete"
        )
        print("方法6: 页面加载完成")
        
        # 额外等待一段时间确保iframe完全渲染
        time.sleep(3)
        
        # 尝试切换iframe
        iframe = WebDriverWait(browser, timeout).until(
            EC.frame_to_be_available_and_switch_to_it((By.XPATH, "//iframe[contains(@name, 'opendoc-')]"))
        )
        print("方法6: 成功切换到iframe")
        return True
    except TimeoutException:
        print("方法6: 等待页面加载后切换失败")
        return False

def try_all_methods(browser):
    """
    尝试所有方法切换iframe
    """
    methods = [
        switch_to_iframe_method1,
        switch_to_iframe_method2,
        switch_to_iframe_method3,
        switch_to_iframe_method4,
        switch_to_iframe_method5,
        switch_to_iframe_method6,
        switch_to_iframe_method7
    ]
    
    for i, method in enumerate(methods, 1):
        print(f"\n=== 尝试方法 {i} ===")
        try:
            # 确保回到主文档
            browser.switch_to.default_content()
            
            if method(browser):
                print(f"✅ 方法 {i} 成功!")
                return True
        except Exception as e:
            print(f"❌ 方法 {i} 出现异常: {e}")
            continue
    
    print("\n❌ 所有方法都失败了")
    return False

def debug_iframe_info(browser):
    """
    调试函数：打印页面中所有iframe的信息
    """
    try:
        print("\n=== 调试信息：查找页面中的所有iframe ===")

        # 回到主文档
        browser.switch_to.default_content()

        # 查找所有iframe
        iframes = browser.find_elements(By.TAG_NAME, "iframe")
        print(f"找到 {len(iframes)} 个iframe:")

        for i, iframe in enumerate(iframes):
            name = iframe.get_attribute("name") or "无name属性"
            src = iframe.get_attribute("src") or "无src属性"
            id_attr = iframe.get_attribute("id") or "无id属性"
            print(f"  iframe {i+1}:")
            print(f"    name: {name}")
            print(f"    id: {id_attr}")
            print(f"    src: {src[:100]}..." if len(src) > 100 else f"    src: {src}")
            print()

    except Exception as e:
        print(f"调试信息获取失败: {e}")

def debug_detailed_structure(browser):
    """
    详细调试函数：分析DOM结构和iframe的可见性
    """
    try:
        print("\n=== 详细调试：分析DOM结构 ===")

        # 回到主文档
        browser.switch_to.default_content()

        # 检查lark_doc容器
        lark_doc = browser.find_element(By.ID, "lark_doc")
        print(f"lark_doc容器存在: {lark_doc is not None}")
        print(f"lark_doc是否可见: {lark_doc.is_displayed()}")

        # 检查tt-docs-component
        tt_docs_elements = browser.find_elements(By.TAG_NAME, "tt-docs-component")
        print(f"找到 {len(tt_docs_elements)} 个tt-docs-component")

        for i, tt_docs in enumerate(tt_docs_elements):
            print(f"tt-docs-component {i+1}:")
            print(f"  是否可见: {tt_docs.is_displayed()}")
            print(f"  是否启用: {tt_docs.is_enabled()}")

            # 查找这个component内的iframe
            inner_iframes = tt_docs.find_elements(By.TAG_NAME, "iframe")
            print(f"  内部iframe数量: {len(inner_iframes)}")

            for j, inner_iframe in enumerate(inner_iframes):
                name = inner_iframe.get_attribute("name") or "无name"
                is_displayed = inner_iframe.is_displayed()
                is_enabled = inner_iframe.is_enabled()
                print(f"    iframe {j+1}: name={name[:30]}..., 可见={is_displayed}, 启用={is_enabled}")

        # 等待一段时间后再次检查
        print("\n等待3秒后再次检查...")
        time.sleep(3)

        # 再次查找所有iframe
        all_iframes = browser.find_elements(By.TAG_NAME, "iframe")
        print(f"3秒后找到 {len(all_iframes)} 个iframe:")

        for i, iframe in enumerate(all_iframes):
            name = iframe.get_attribute("name") or "无name"
            is_displayed = iframe.is_displayed()
            is_enabled = iframe.is_enabled()
            print(f"  iframe {i+1}: name={name[:30]}..., 可见={is_displayed}, 启用={is_enabled}")

            # 尝试获取iframe的位置和大小
            try:
                location = iframe.location
                size = iframe.size
                print(f"    位置: {location}, 大小: {size}")
            except Exception as e:
                print(f"    无法获取位置信息: {e}")

    except Exception as e:
        print(f"详细调试失败: {e}")

def switch_to_iframe_method7(browser, timeout=30):
    """
    方法7: 等待更长时间并使用JavaScript辅助
    """
    try:
        print("方法7: 使用JavaScript辅助查找iframe...")

        # 等待页面完全加载
        WebDriverWait(browser, timeout).until(
            lambda driver: driver.execute_script("return document.readyState") == "complete"
        )

        # 使用JavaScript查找iframe
        js_code = """
        var iframes = document.querySelectorAll('iframe');
        var result = [];
        for (var i = 0; i < iframes.length; i++) {
            var iframe = iframes[i];
            result.push({
                index: i,
                name: iframe.name || 'no-name',
                src: iframe.src || 'no-src',
                id: iframe.id || 'no-id',
                visible: iframe.offsetWidth > 0 && iframe.offsetHeight > 0
            });
        }
        return result;
        """

        iframe_info = browser.execute_script(js_code)
        print(f"方法7: JavaScript找到 {len(iframe_info)} 个iframe:")

        for info in iframe_info:
            print(f"  iframe {info['index']}: name={info['name']}, visible={info['visible']}")

        # 尝试切换到第一个可见的iframe
        for info in iframe_info:
            if info['visible'] and 'opendoc' in info['name']:
                print(f"方法7: 尝试切换到iframe: {info['name']}")
                try:
                    iframe_element = browser.execute_script(f"return document.querySelectorAll('iframe')[{info['index']}]")
                    browser.switch_to.frame(iframe_element)
                    print("方法7: 成功切换到iframe")
                    return True
                except Exception as e:
                    print(f"方法7: 切换失败: {e}")
                    browser.switch_to.default_content()

        # 如果没有找到opendoc的iframe，尝试所有可见的iframe
        for info in iframe_info:
            if info['visible']:
                print(f"方法7: 尝试切换到可见iframe: {info['name']}")
                try:
                    iframe_element = browser.execute_script(f"return document.querySelectorAll('iframe')[{info['index']}]")
                    browser.switch_to.frame(iframe_element)
                    print("方法7: 成功切换到iframe")
                    return True
                except Exception as e:
                    print(f"方法7: 切换失败: {e}")
                    browser.switch_to.default_content()

        print("方法7: 没有找到可切换的iframe")
        return False

    except Exception as e:
        print(f"方法7: 出现异常: {e}")
        return False

# 使用示例
if __name__ == "__main__":
    # 假设您已经有了browser对象
    # browser = webdriver.Chrome()  # 或其他浏览器
    # browser.get("your_url_here")
    
    # 调试iframe信息
    # debug_iframe_info(browser)
    
    # 尝试所有方法
    # success = try_all_methods(browser)
    
    # if success:
    #     print("成功切换到iframe，可以继续后续操作")
    # else:
    #     print("无法切换到iframe，请检查页面结构")
    
    pass
