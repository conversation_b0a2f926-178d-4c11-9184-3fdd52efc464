
import requests
import json
import hashlib
import time

base_url = 'https://restapi.getui.com/v2/xwdkpQLlfk8Azm2eErDqq9'

# 配置参数
APPKEY = "sxa6Dr0Fpq5lTcxQgB5LS1"
MASTER_SECRET = "FVLWoYSpcw6DmolUgqz76A"

def generate_sign(appkey, timestamp, master_secret):
    """
    生成签名
    sign值 = 将 appkey、timestamp、mastersecret 对应的字符串按此固定顺序拼接，使用 SHA256 算法加密。
    示例 java 代码格式: String sign = sha256(appkey+timestamp+mastersecret)

    Args:
        appkey (str): 应用密钥
        timestamp (str): 时间戳
        master_secret (str): 主密钥

    Returns:
        str: SHA256签名
    """
    # 按固定顺序拼接字符串
    sign_str = appkey + timestamp + master_secret

    # 使用SHA256加密
    sign = hashlib.sha256(sign_str.encode('utf-8')).hexdigest()

    return sign

def get_token():
    """
    获取token的方法，发送POST请求
    自动生成timestamp和sign参数

    Returns:
        dict: 响应结果
    """
    token_url = f'{base_url}/auth'

    # 生成当前时间戳（毫秒）
    timestamp = str(int(time.time() * 1000))

    # 生成签名
    sign = generate_sign(APPKEY, timestamp, MASTER_SECRET)

    # 请求头设置
    headers = {
        'Content-Type': 'application/json;charset=utf-8'
    }

    # 请求参数
    payload = {
        "sign": sign,
        "timestamp": timestamp,
        "appkey": APPKEY
    }

    print(f"请求参数: {payload}")

    try:
        # 发送POST请求
        response = requests.post(
            url=token_url,
            headers=headers,
            data=json.dumps(payload)
        )

        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")

        # 解析JSON响应
        result = response.json()

        # 检查响应是否成功
        if result.get('code') == 0 and result.get('msg') == 'success':
            token = result.get('data', {}).get('token')
            expire_time = result.get('data', {}).get('expire_time')

            print(f"获取token成功!")
            print(f"Token: {token}")
            print(f"过期时间: {expire_time}")

            return token
        else:
            print(f"获取token失败: {result}")
            return None

    except requests.exceptions.RequestException as e:
        print(f"请求失败: {e}")
        return None
    except json.JSONDecodeError as e:
        print(f"JSON解析失败: {e}")
        return None

def cid_push(token):
    """
    根据cid推送消息的方法，发送POST请求
    自动生成timestamp和sign参数

    Args:
        token (str): 认证令牌

    Returns:
        dict: 响应结果
    """
    cid_push_url = f'{base_url}/push/single/cid'


if __name__ == '__main__':
    print(get_token())
