from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException
import time

def debug_and_switch_iframe(browser, timeout=30):
    """
    专门针对您的问题的调试和切换方法
    """
    print("=== 开始调试iframe问题 ===")
    
    try:
        # 确保回到主文档
        browser.switch_to.default_content()
        
        # 第1步：等待页面完全加载
        print("第1步：等待页面完全加载...")
        WebDriverWait(browser, timeout).until(
            lambda driver: driver.execute_script("return document.readyState") == "complete"
        )
        print("✅ 页面加载完成")
        
        # 第2步：等待lark_doc容器
        print("第2步：等待lark_doc容器...")
        lark_doc = WebDriverWait(browser, timeout).until(
            EC.presence_of_element_located((By.ID, "lark_doc"))
        )
        print("✅ lark_doc容器已找到")
        
        # 第3步：等待tt-docs-component
        print("第3步：等待tt-docs-component...")
        WebDriverWait(browser, timeout).until(
            EC.presence_of_element_located((By.TAG_NAME, "tt-docs-component"))
        )
        print("✅ tt-docs-component已找到")
        
        # 第4步：等待一段时间让iframe完全渲染
        print("第4步：等待iframe渲染...")
        time.sleep(5)  # 等待5秒
        
        # 第5步：使用JavaScript查找所有iframe
        print("第5步：使用JavaScript查找iframe...")
        js_find_iframes = """
        var iframes = document.querySelectorAll('iframe');
        var result = [];
        for (var i = 0; i < iframes.length; i++) {
            var iframe = iframes[i];
            var rect = iframe.getBoundingClientRect();
            result.push({
                index: i,
                name: iframe.name || '',
                src: iframe.src || '',
                id: iframe.id || '',
                visible: rect.width > 0 && rect.height > 0,
                width: rect.width,
                height: rect.height,
                top: rect.top,
                left: rect.left
            });
        }
        return result;
        """
        
        iframe_list = browser.execute_script(js_find_iframes)
        print(f"找到 {len(iframe_list)} 个iframe:")
        
        for i, iframe_info in enumerate(iframe_list):
            print(f"  iframe {i}: ")
            print(f"    name: {iframe_info['name']}")
            print(f"    visible: {iframe_info['visible']}")
            print(f"    size: {iframe_info['width']}x{iframe_info['height']}")
            print(f"    position: ({iframe_info['left']}, {iframe_info['top']})")
            print()
        
        # 第6步：尝试切换到目标iframe
        print("第6步：尝试切换iframe...")
        
        # 优先尝试包含opendoc的iframe
        target_iframe = None
        for iframe_info in iframe_list:
            if 'opendoc' in iframe_info['name'] and iframe_info['visible']:
                target_iframe = iframe_info
                break
        
        # 如果没找到opendoc的，尝试第一个可见的iframe
        if not target_iframe:
            for iframe_info in iframe_list:
                if iframe_info['visible']:
                    target_iframe = iframe_info
                    break
        
        if target_iframe:
            print(f"找到目标iframe: {target_iframe['name']}")
            
            # 方法A：使用索引切换
            try:
                iframe_element = browser.execute_script(f"return document.querySelectorAll('iframe')[{target_iframe['index']}]")
                browser.switch_to.frame(iframe_element)
                print("✅ 方法A成功：使用索引切换iframe")
                return True
            except Exception as e:
                print(f"❌ 方法A失败: {e}")
                browser.switch_to.default_content()
            
            # 方法B：使用name属性切换
            if target_iframe['name']:
                try:
                    WebDriverWait(browser, 10).until(
                        EC.frame_to_be_available_and_switch_to_it((By.NAME, target_iframe['name']))
                    )
                    print("✅ 方法B成功：使用name属性切换iframe")
                    return True
                except Exception as e:
                    print(f"❌ 方法B失败: {e}")
                    browser.switch_to.default_content()
            
            # 方法C：使用XPath切换
            try:
                xpath = f"//iframe[@name='{target_iframe['name']}']" if target_iframe['name'] else f"(//iframe)[{target_iframe['index']+1}]"
                WebDriverWait(browser, 10).until(
                    EC.frame_to_be_available_and_switch_to_it((By.XPATH, xpath))
                )
                print("✅ 方法C成功：使用XPath切换iframe")
                return True
            except Exception as e:
                print(f"❌ 方法C失败: {e}")
                browser.switch_to.default_content()
        
        print("❌ 没有找到可用的iframe")
        return False
        
    except Exception as e:
        print(f"❌ 调试过程出现异常: {e}")
        return False

def wait_and_retry_iframe_switch(browser, max_retries=3, wait_between_retries=10):
    """
    多次重试切换iframe
    """
    for attempt in range(max_retries):
        print(f"\n=== 第 {attempt + 1} 次尝试切换iframe ===")
        
        if debug_and_switch_iframe(browser):
            print(f"✅ 第 {attempt + 1} 次尝试成功!")
            return True
        
        if attempt < max_retries - 1:
            print(f"第 {attempt + 1} 次尝试失败，{wait_between_retries}秒后重试...")
            time.sleep(wait_between_retries)
    
    print(f"❌ {max_retries} 次尝试都失败了")
    return False

# 使用示例
def main():
    """
    主函数示例
    """
    # 假设您已经有了browser对象
    # browser = webdriver.Chrome()
    # browser.get("your_url")
    
    # 方法1：单次尝试
    # success = debug_and_switch_iframe(browser)
    
    # 方法2：多次重试
    # success = wait_and_retry_iframe_switch(browser, max_retries=3, wait_between_retries=10)
    
    # if success:
    #     print("成功切换到iframe，可以继续操作")
    #     # 在这里添加您的后续操作
    # else:
    #     print("无法切换到iframe")
    
    pass

if __name__ == "__main__":
    main()
