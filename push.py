
import requests
import json
import hashlib
import time
import random
import string

base_url = 'https://restapi.getui.com/v2/Rti8eljkzx9ojQNDbAlJp3'

# 配置参数
APPKEY = "C7V5lk3WK46sLY0PbuJkw3"
MASTER_SECRET = "rEfmNRg1DG6QN68S9gOAx1"

def generate_sign(appkey, timestamp, master_secret):
    """
    生成签名
    sign值 = 将 appkey、timestamp、mastersecret 对应的字符串按此固定顺序拼接，使用 SHA256 算法加密。
    示例 java 代码格式: String sign = sha256(appkey+timestamp+mastersecret)

    Args:
        appkey (str): 应用密钥
        timestamp (str): 时间戳
        master_secret (str): 主密钥

    Returns:
        str: SHA256签名
    """
    # 按固定顺序拼接字符串
    sign_str = appkey + timestamp + master_secret

    # 使用SHA256加密
    sign = hashlib.sha256(sign_str.encode('utf-8')).hexdigest()

    return sign

def get_token():
    """
    获取token的方法，发送POST请求
    自动生成timestamp和sign参数

    Returns:
        dict: 响应结果
    """
    token_url = f'{base_url}/auth'

    # 生成当前时间戳（毫秒）
    timestamp = str(int(time.time() * 1000))

    # 生成签名
    sign = generate_sign(APPKEY, timestamp, MASTER_SECRET)

    # 请求头设置
    headers = {
        'Content-Type': 'application/json;charset=utf-8'
    }

    # 请求参数
    payload = {
        "sign": sign,
        "timestamp": timestamp,
        "appkey": APPKEY
    }

    print(f"请求参数: {payload}")

    try:
        # 发送POST请求
        response = requests.post(
            url=token_url,
            headers=headers,
            data=json.dumps(payload)
        )

        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")

        # 解析JSON响应
        result = response.json()

        # 检查响应是否成功
        if result.get('code') == 0 and result.get('msg') == 'success':
            token = result.get('data', {}).get('token')
            expire_time = result.get('data', {}).get('expire_time')

            print(f"获取token成功!")
            print(f"Token: {token}")
            print(f"过期时间: {expire_time}")

            return token
        else:
            print(f"获取token失败: {result}")
            return None

    except requests.exceptions.RequestException as e:
        print(f"请求失败: {e}")
        return None
    except json.JSONDecodeError as e:
        print(f"JSON解析失败: {e}")
        return None

def generate_request_id(length=16):
    """
    生成随机的request_id

    Args:
        length (int): 生成的字符串长度，默认16位，范围10-32位

    Returns:
        str: 随机生成的request_id
    """
    if length < 10:
        length = 10
    elif length > 32:
        length = 32

    # 生成随机字符串，包含字母和数字
    characters = string.ascii_letters + string.digits
    request_id = ''.join(random.choice(characters) for _ in range(length))

    return request_id

def cid_push(token, cid, title, body, click_url=None, request_id=None, group_name=None, ttl=7200000):
    """
    根据cid推送消息的方法，发送POST请求

    Args:
        token (str): 认证令牌
        cid (str): 客户端ID
        title (str): 通知标题
        body (str): 通知内容
        click_url (str, optional): 点击跳转链接
        request_id (str, optional): 请求唯一标识号，10-32位之间，如果不提供会自动生成
        group_name (str, optional): 任务组名
        ttl (int, optional): 消息离线时间，默认7200000毫秒(2小时)

    Returns:
        dict: 响应结果
    """
    cid_push_url = f'{base_url}/push/single/cid'

    # 如果没有提供request_id，自动生成一个
    if not request_id:
        request_id = generate_request_id(16)  # 生成16位的request_id

    # 请求头设置
    headers = {
        'Content-Type': 'application/json;charset=utf-8',
        'token': token
    }

    # 构建推送消息体
    push_message = {
        "notification": {
            "title": title,
            "body": body,
            "click_type": "url" if click_url else "none"
        }
    }

    push_channel = {

    }

    # 如果有点击链接，添加到消息体中
    if click_url:
        push_message["notification"]["url"] = click_url

    # 构建请求参数
    payload = {
        "settings": {
            "ttl": ttl
        },
        "audience": {
            "cid": [cid]
        },
        "push_message": push_message,
        "push_channel": push_channel
    }

    # 添加request_id（现在总是会有值）
    payload["request_id"] = request_id

    if group_name:
        payload["group_name"] = group_name

    print(f"CID推送请求参数: {json.dumps(payload, indent=2, ensure_ascii=False)}")

    try:
        # 发送POST请求
        response = requests.post(
            url=cid_push_url,
            headers=headers,
            data=json.dumps(payload, ensure_ascii=False)
        )

        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")

        # 解析JSON响应
        result = response.json()

        # 检查响应是否成功
        if result.get('code') == 0:
            print(f"CID推送成功!")
            if 'data' in result:
                print(f"推送结果: {result['data']}")
            return result
        else:
            print(f"CID推送失败: {result}")
            return result

    except requests.exceptions.RequestException as e:
        print(f"请求失败: {e}")
        return {"error": str(e)}
    except json.JSONDecodeError as e:
        print(f"JSON解析失败: {e}")
        return {"error": str(e)}


def send_push_notification(cid, title, body, click_url=None):
    """
    发送推送通知的便捷方法
    自动获取token并发送推送

    Args:
        cid (str): 客户端ID
        title (str): 通知标题
        body (str): 通知内容
        click_url (str, optional): 点击跳转链接

    Returns:
        dict: 推送结果
    """
    # 先获取token
    token = get_token()
    if not token:
        return {"error": "获取token失败"}

    # 发送推送
    result = cid_push(
        token=token,
        cid=cid,
        title=title,
        body=body,
        click_url=click_url
    )

    return result

if __name__ == '__main__':
    # 测试获取token
    print("=== 测试获取Token ===")
    token = get_token()
    print(f"Token: {token}")

    # 测试推送消息（需要替换为真实的CID）
    if token:
        print("\n=== 测试CID推送 ===")
        test_cid = "7a0be772d6278e01faa22a4facfcde25"  # 替换为真实的CID
        result = cid_push(
            token=token,
            cid=test_cid,
            title="测试通知标题",
            body="这是测试通知内容",
            click_url="https://www.example.com",
            group_name='test_group_1'
        )
        print(f"推送结果: {result}")
