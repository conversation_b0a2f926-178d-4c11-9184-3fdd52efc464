import time
from selenium.webdriver.support import expected_conditions as EC

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver import ActionChains
from selenium.webdriver.support.wait import WebDriverWait


def login_with_channel():
    # 登录网站
    login_url = f'https://game.open.douyin.com/platform?'
    login_username = '<EMAIL>'
    login_password = 'zls062813'

    # 防止打开浏览器后闪退
    options = webdriver.ChromeOptions()
    options.add_experimental_option('detach', True)
    #options.add_argument('--headless')  # 无头模式
    # 设置为开发者模式，防止被各大网站识别出来使用了Selenium
    options.add_experimental_option('excludeSwitches', ['enable-automation'])

    browser = webdriver.Chrome(options=options)
    browser.get(login_url)
    # 设置window.navigator.webdriver，希望能绕过滑块验证
    browser.execute_script('Object.defineProperties(navigator,{webdriver:{get:()=>false}})')

    # 展示登录
    show_login_btn = browser.find_element(By.CLASS_NAME, 'home-header-login-Webx0Uvk')
    show_login_btn.click()
    time.sleep(2)

    # 查找用户名和密码输入框所在元素
    username_input = browser.find_element(By.ID, 'account')
    username_input.click()
    username_input.send_keys(login_username)
    # 等待
    time.sleep(2)

    password_input = browser.find_element(By.ID, "password")
    password_input.click()
    password_input.send_keys(login_password)
    time.sleep(2)

    # 点击登录按钮
    login_btn = browser.find_element(By.CLASS_NAME, "game-button-v2")
    login_btn.click()
    time.sleep(2)

    # 重新打开文档
    dy_update_document_url = 'https://game.open.douyin.com/platform/subapp/live/learning_center/detail/doc/?id=185&tab=X4g4fKRNtlzPddd3CPkcR2PBnKh&versions=b3x8mI'
    browser.get(dy_update_document_url)
    time.sleep(2)

    iframe = WebDriverWait(browser, 10).until(
        EC.frame_to_be_available_and_switch_to_it((By.XPATH, "//div[@id='lark_doc']//tt-docs-component//div//iframe"))
    )

    # 检查版本  author-7111139344303357954
    version_span = browser.find_element(By.CLASS_NAME, "author-7111139344303357954")
    print(f'版本：{version_span.text}')

    # browser.switch_to.default_content()  # 返回主文档

    # # 滑块处理
    #
    # browser.switch_to.frame('baxia-dialog-content')
    # slider = browser.find_element(by=By.XPATH, value='//*[@id="nc_1_n1z"]')
    #
    # slider_block = browser.find_element(by=By.XPATH, value='//*[@id="nc_1__scale_text"]')
    #
    # chains = ActionChains(browser)
    # chains.drag_and_drop_by_offset(slider, slider_block.size['width'], -slider_block.size['height'])
    # chains.perform()
    # time.sleep(2)
    #
    # # 查找按钮元素，模拟点击
    # #点击登录按钮
    # browser.find_element(By.XPATH, ".//*[@id='login-form']/div[4]/button").click()
    # browser.implicitly_wait(10)
    #
    # # 关闭浏览器
    # browser.close()
    # browser.quit()

if __name__ == '__main__':
    print('login start')
    login_with_channel()